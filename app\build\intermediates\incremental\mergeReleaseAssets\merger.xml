<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="wind-sdk-4.23.0.aar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b66139be6585a43ae23903a8750be796\transformed\wind-sdk-4.23.0\assets"><file name="sig_appelements.html" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b66139be6585a43ae23903a8750be796\transformed\wind-sdk-4.23.0\assets\sig_appelements.html"/></source></dataSet><dataSet config="open_ad_sdk_6.8.4.0.aar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\077c69690200a4c0c0ddf0630ba2f6e7\transformed\open_ad_sdk_6.8.4.0\assets"><file name="lottie_json/shake_phone.json" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\077c69690200a4c0c0ddf0630ba2f6e7\transformed\open_ad_sdk_6.8.4.0\assets\lottie_json\shake_phone.json"/><file name="lottie_json/swipe_right.json" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\077c69690200a4c0c0ddf0630ba2f6e7\transformed\open_ad_sdk_6.8.4.0\assets\lottie_json\swipe_right.json"/><file name="lottie_json/twist_multi_angle.json" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\077c69690200a4c0c0ddf0630ba2f6e7\transformed\open_ad_sdk_6.8.4.0\assets\lottie_json\twist_multi_angle.json"/><file name="na.czl" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\077c69690200a4c0c0ddf0630ba2f6e7\transformed\open_ad_sdk_6.8.4.0\assets\na.czl"/><file name="uc_dsl/uc_dsl.bin" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\077c69690200a4c0c0ddf0630ba2f6e7\transformed\open_ad_sdk_6.8.4.0\assets\uc_dsl\uc_dsl.bin"/></source></dataSet><dataSet config="kssdk-ad-********-publishRelease-aa0a55f514.aar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb0f3cc3125274cec1b491c0584a407c\transformed\kssdk-ad-********-publishRelease-aa0a55f514\assets"><file name="ksad_common_encrypt_image.png" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb0f3cc3125274cec1b491c0584a407c\transformed\kssdk-ad-********-publishRelease-aa0a55f514\assets\ksad_common_encrypt_image.png"/><file name="ksad_idc.json" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cb0f3cc3125274cec1b491c0584a407c\transformed\kssdk-ad-********-publishRelease-aa0a55f514\assets\ksad_idc.json"/></source></dataSet><dataSet config="GDTSDK.unionNormal.4.640.1510.aar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2144dbf5c98623e2472a5ef01095ffd5\transformed\GDTSDK.unionNormal.4.640.1510\assets"><file name="gdt_plugin/gdtadv2.jar" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2144dbf5c98623e2472a5ef01095ffd5\transformed\GDTSDK.unionNormal.4.640.1510\assets\gdt_plugin\gdtadv2.jar"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets"><file name="events.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events.json"/><file name="events_en.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_en.json"/><file name="events_zh_tw.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_zh_tw.json"/><file name="events_ja.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_ja.json"/><file name="events_ko.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_ko.json"/><file name="events_de.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_de.json"/><file name="events_es.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_es.json"/><file name="events_fr.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_fr.json"/><file name="events_hi.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_hi.json"/><file name="events_in.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_in.json"/><file name="events_it.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_it.json"/><file name="events_ms.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_ms.json"/><file name="events_pt.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_pt.json"/><file name="events_ru.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_ru.json"/><file name="events_th.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_th.json"/><file name="events_vi.json" path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\assets\events_vi.json"/></source></dataSet><dataSet config="release" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Ai\AiCode\game\MountainSurvival\code\app\src\release\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="E:\Ai\AiCode\game\MountainSurvival\code\app\build\intermediates\shader_assets\release\compileReleaseShaders\out"/></dataSet></merger>