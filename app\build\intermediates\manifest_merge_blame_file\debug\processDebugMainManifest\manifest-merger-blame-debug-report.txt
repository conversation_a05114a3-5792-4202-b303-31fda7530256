1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ainative.mountainsurvival"
4    android:versionCode="3"
5    android:versionName="1.2.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <!-- 必要权限：应用基本功能所需 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:6:5-66
12-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:7:5-78
13-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- 广告优化权限：可选，用于提升广告效果 -->
16    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
16-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:10:5-75
16-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:10:22-73
17
18    <permission
18-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
19        android:name="com.ainative.mountainsurvival.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
20        android:protectionLevel="signature" />
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
21
22    <uses-permission android:name="com.ainative.mountainsurvival.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- 以下权限已移除，因为单机游戏不需要这些敏感权限 -->
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
22-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
23    <!-- <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/> -->
24    <!-- <uses-permission android:name="android.permission.READ_PHONE_STATE"/> -->
25    <!-- <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/> -->
26    <!-- <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/> -->
27    <!-- <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/> -->
28    <application
28-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:19:5-80:19
29        android:name="com.ainative.mountainsurvival.MountainSurvivalApplication"
29-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:20:9-52
30        android:allowBackup="true"
30-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:21:9-35
31        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
31-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b7d68e85c64c5740c775d2c6236c08f5\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
32        android:dataExtractionRules="@xml/data_extraction_rules"
32-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:22:9-65
33        android:debuggable="true"
34        android:extractNativeLibs="false"
35        android:fullBackupContent="@xml/backup_rules"
35-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:23:9-54
36        android:icon="@mipmap/ic_launcher"
36-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:24:9-43
37        android:label="@string/app_name"
37-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:25:9-41
38        android:networkSecurityConfig="@xml/network_security_config"
38-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:29:9-69
39        android:roundIcon="@mipmap/ic_launcher_round"
39-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:26:9-54
40        android:supportsRtl="true"
40-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:27:9-35
41        android:theme="@style/Theme.MountainSurvival" >
41-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:28:9-54
42
43        <!-- 开始界面 - 启动Activity -->
44        <activity
44-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:33:9-43:20
45            android:name="com.ainative.mountainsurvival.StartActivity"
45-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:34:13-42
46            android:exported="true"
46-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:35:13-36
47            android:label="@string/app_name"
47-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:36:13-45
48            android:theme="@style/Theme.MountainSurvival" >
48-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:37:13-58
49            <intent-filter>
49-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:38:13-42:29
50                <action android:name="android.intent.action.MAIN" />
50-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:39:17-69
50-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:39:25-66
51
52                <category android:name="android.intent.category.LAUNCHER" />
52-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:41:17-77
52-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:41:27-74
53            </intent-filter>
54        </activity>
55
56        <!-- 隐私政策界面 -->
57        <activity
57-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:46:9-50:61
58            android:name="com.ainative.mountainsurvival.PrivacyPolicyActivity"
58-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:47:13-50
59            android:exported="false"
59-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:48:13-37
60            android:label="@string/app_name"
60-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:49:13-45
61            android:theme="@style/Theme.MountainSurvival" />
61-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:50:13-58
62
63        <!-- 主游戏界面 -->
64        <activity
64-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:53:9-57:61
65            android:name="com.ainative.mountainsurvival.MainActivity"
65-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:54:13-41
66            android:exported="false"
66-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:55:13-37
67            android:label="@string/app_name"
67-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:56:13-45
68            android:theme="@style/Theme.MountainSurvival" />
68-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:57:13-58
69
70        <!-- Support库FileProvider 兼容广告SDK -->
71        <provider
72            android:name="android.support.v4.content.FileProvider"
72-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:61:13-67
73            android:authorities="com.ainative.mountainsurvival.fileprovider"
73-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:62:13-64
74            android:exported="false"
74-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:63:13-37
75            android:grantUriPermissions="true" >
75-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:64:13-47
76            <meta-data
76-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:65:13-67:54
77                android:name="android.support.FILE_PROVIDER_PATHS"
77-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:66:17-67
78                android:resource="@xml/file_paths" />
78-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:67:17-51
79        </provider>
80
81        <!-- 广告SDK专用的FileProvider -->
82        <provider
83            android:name="android.support.v4.content.FileProvider"
83-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:61:13-67
84            android:authorities="com.ainative.mountainsurvival.osetfileprovider"
84-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:62:13-64
85            android:exported="false"
85-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:63:13-37
86            android:grantUriPermissions="true" >
86-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:64:13-47
87            <meta-data
87-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:65:13-67:54
88                android:name="android.support.FILE_PROVIDER_PATHS"
88-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:66:17-67
89                android:resource="@xml/oset_file_paths" />
89-->E:\Ai\AiCode\game\MountainSurvival\code\app\src\main\AndroidManifest.xml:67:17-51
90        </provider>
91        <provider
91-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
92            android:name="androidx.startup.InitializationProvider"
92-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
93            android:authorities="com.ainative.mountainsurvival.androidx-startup"
93-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
94            android:exported="false" >
94-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
95            <meta-data
95-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.emoji2.text.EmojiCompatInitializer"
96-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
97                android:value="androidx.startup" />
97-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\125fab410ec46f97b93aee73d09333fb\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
98            <meta-data
98-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:31:13-33:52
99                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
99-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:32:17-78
100                android:value="androidx.startup" />
100-->[androidx.lifecycle:lifecycle-process:2.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\97801c6c507d8e8a69f1aae422f6dcba\transformed\jetified-lifecycle-process-2.4.1\AndroidManifest.xml:33:17-49
101        </provider> <!-- Just used for obtain context -->
102        <provider
102-->[io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:17:9-20:40
103            android:name="com.aliyun.sls.android.producer.provider.SLSContentProvider"
103-->[io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:18:13-87
104            android:authorities="com.ainative.mountainsurvival.sls_provider"
104-->[io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:19:13-64
105            android:exported="false" />
105-->[io.github.aliyun-sls:aliyun-log-android-sdk:2.7.13] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c4d0163aaba9a980e61cd3d3add9946e\transformed\jetified-aliyun-log-android-sdk-2.7.13\AndroidManifest.xml:20:13-37
106    </application>
107
108</manifest>
