<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.ainative.mountainsurvival"
    android:versionCode="3"
    android:versionName="1.2.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="35" />

    <!-- 必要权限：应用基本功能所需 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- 广告优化权限：可选，用于提升广告效果 -->
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />

    <permission
        android:name="com.ainative.mountainsurvival.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.ainative.mountainsurvival.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- 以下权限已移除，因为单机游戏不需要这些敏感权限 -->
    <!-- <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/> -->
    <!-- <uses-permission android:name="android.permission.READ_PHONE_STATE"/> -->
    <!-- <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/> -->
    <!-- <uses-permission android:name="android.permission.REQUEST_INSTALL_PACKAGES"/> -->
    <!-- <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/> -->
    <application
        android:name="com.ainative.mountainsurvival.MountainSurvivalApplication"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:networkSecurityConfig="@xml/network_security_config"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.MountainSurvival" >

        <!-- 开始界面 - 启动Activity -->
        <activity
            android:name="com.ainative.mountainsurvival.StartActivity"
            android:exported="true"
            android:label="@string/app_name"
            android:theme="@style/Theme.MountainSurvival" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 隐私政策界面 -->
        <activity
            android:name="com.ainative.mountainsurvival.PrivacyPolicyActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.MountainSurvival" />

        <!-- 主游戏界面 -->
        <activity
            android:name="com.ainative.mountainsurvival.MainActivity"
            android:exported="false"
            android:label="@string/app_name"
            android:theme="@style/Theme.MountainSurvival" />

        <!-- Support库FileProvider 兼容广告SDK -->
        <provider
            android:name="android.support.v4.content.FileProvider"
            android:authorities="com.ainative.mountainsurvival.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

        <!-- 广告SDK专用的FileProvider -->
        <provider
            android:name="android.support.v4.content.FileProvider"
            android:authorities="com.ainative.mountainsurvival.osetfileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/oset_file_paths" />
        </provider>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.ainative.mountainsurvival.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
        </provider> <!-- Just used for obtain context -->
        <provider
            android:name="com.aliyun.sls.android.producer.provider.SLSContentProvider"
            android:authorities="com.ainative.mountainsurvival.sls_provider"
            android:exported="false" />
    </application>

</manifest>